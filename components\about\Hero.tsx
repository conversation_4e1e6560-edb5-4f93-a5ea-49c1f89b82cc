
'use client';

import Image from 'next/image';
import AboutVideo from './About-video';

const AboutHero = () => {
  return (
    <div className="w-full min-h-screen bg-white relative flex">

      <div className="absolute inset-0 z-0 bg-[url('/grid.png')] bg-repeat opacity-60"></div>

      {/* Navbar - Hidden on small and medium screens, visible on large screens */}
      <div className="fixed left-0 top-0 h-full w-16 bg-black flex-col items-center justify-center space-y-8 z-10 hidden lg:flex">
        <div className="w-6 h-6 bg-cyan-400 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
      </div>

      {/* Main Content Area - Adjusted margin for small/medium screens */}
      <div className="flex-1 ml-0 lg:ml-16 bg-transparent relative overflow-hidden">

        {/* Logo - Responsive positioning to match home page */}
        <div className="mx-auto pl-[5%]">
      <div className="flex items-center gap-2 mt-5">
        <img
          src="/logo-icon.png"
          alt="Logo Icon"
          className="w-10 h-10"
        />
        <div>
          <h1 className="text-black font-bold text-xl md:text-2xl leading-tight">
            Prolytech
          </h1>
          <p className="text-[#05A0E2] text-[5.5px] font-bold tracking-wide">
            DEFINING THE CURVE OF WHAT'S NEXT
          </p>
        </div>
      </div>
    </div>

        {/* SVG clipPath for the blob, necessary for the video */}
        <svg width="0" height="0">
          <defs>
            <clipPath
              id="blobClip"
              clipPathUnits="objectBoundingBox"
              transform="scale(0.0017, 0.0016)"
            >
              <path d="M81.5702 435.071C122.524 497.307 132.82 569.329 199.46 602.679C276.32 641.145 332.063 562.008 372.109 530.182C404.709 504.272 539.123 512.181 568.025 482.205C616.135 432.309 560.186 383.738 538.312 317.98C521.208 266.562 542.548 186.363 523.087 135.788C495.179 63.2584 475.03 13.9755 398.118 2.63276C332.931 -6.98075 307.096 10.7407 247.773 39.409C176.483 73.8611 73.3626 93.2439 31.9879 160.73C-0.113567 213.091 -10.6617 261.071 12.4247 317.98C33.709 370.448 50.4438 387.769 81.5702 435.071Z" />
            </clipPath>
          </defs>
        </svg>

        {/* Hero Content - Centered Layout */}
        <div className="w-full md:px-8 lg:px-20 py-8 md:py-12 lg:py-16 relative z-10 flex items-center justify-center min-h-screen">
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center gap-6 md:gap-8 text-black w-full">
            {/* Left Content */}
            <div className="flex-1 max-w-md px-0 md:px-4 lg:px-8 xl:px-12">
              <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">
                About Us
              </p>
              <h2 className="text-lg sm:text-4xl md:text-[24px] lg:text-[28px] font-normal text-black leading-[1.2]">
                Your Partner in Platform<br />
                <span className="whitespace-nowrap">Engineering & Innovation</span>
              </h2>
              <p className="text-sm md:text-sm text-gray-600 leading-relaxed mt-4">
                Learn how Prolytech delivers transformative software systems—social, transactional,<br />
                and intelligent—for high-growth and high-impact businesses.
              </p>
            </div>

            {/* Right Side Video - Centered */}
            <div className="flex-1 flex justify-center items-center">
              <AboutVideo/>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutHero;
