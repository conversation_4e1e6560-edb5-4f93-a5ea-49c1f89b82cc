
'use client';

import Image from 'next/image';
import AboutBg from '../icons/aboutbg';

const AboutHero = () => {
  return (
    <div className="w-full min-h-screen bg-white relative flex">

      <div className="absolute inset-0 z-0 bg-[url('/grid.png')] bg-repeat opacity-60"></div>

      {/* Navbar - Hidden on small and medium screens, visible on large screens */}
      <div className="fixed left-0 top-0 h-full w-16 bg-black flex-col items-center justify-center space-y-8 z-10 hidden lg:flex">
        <div className="w-6 h-6 bg-cyan-400 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
      </div>

      {/* Main Content Area - Adjusted margin for small/medium screens */}
      <div className="flex-1 ml-0 lg:ml-16 bg-transparent relative overflow-hidden">

        {/* Logo - Aligned with content */}
        <div className="absolute top-6 left-0 right-0 z-20">
          <div className="max-w-7xl mx-auto md:px-8 lg:px-20">
            <div className="px-0 md:px-0 lg:px-4 xl:px-8">
              <div className="flex items-center gap-3">
                <img
                  src="/logo-icon.png"
                  alt="Logo Icon"
                  className="w-8 h-8 md:w-10 md:h-10"
                />
                <div>
                  <h1 className="text-black font-bold text-lg md:text-xl lg:text-2xl leading-tight">
                    Prolytech
                  </h1>
                  <p className="text-[#05A0E2] text-[5px] md:text-[5.5px] font-bold tracking-wide uppercase">
                    DEFINING THE CURVE OF WHAT'S NEXT
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* SVG clipPath for the blob, necessary for the video */}
        <svg width="0" height="0">
          <defs>
            <clipPath
              id="blobClip"
              clipPathUnits="objectBoundingBox"
              transform="scale(0.0017, 0.0016)"
            >
              <path d="M81.5702 435.071C122.524 497.307 132.82 569.329 199.46 602.679C276.32 641.145 332.063 562.008 372.109 530.182C404.709 504.272 539.123 512.181 568.025 482.205C616.135 432.309 560.186 383.738 538.312 317.98C521.208 266.562 542.548 186.363 523.087 135.788C495.179 63.2584 475.03 13.9755 398.118 2.63276C332.931 -6.98075 307.096 10.7407 247.773 39.409C176.483 73.8611 73.3626 93.2439 31.9879 160.73C-0.113567 213.091 -10.6617 261.071 12.4247 317.98C33.709 370.448 50.4438 387.769 81.5702 435.071Z" />
            </clipPath>
          </defs>
        </svg>

        {/* Hero Content */}
        {/* Mobile Layout - Content at bottom */}
        <div className="lg:hidden relative z-10 h-screen flex flex-col">
          {/* Video area - centered */}
          <div className="flex-1 flex justify-center items-center relative -mt-8">
            <div className="relative">
              {/* Background SVG */}
              <div className="absolute z-0 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <AboutBg className="w-[330px] h-[360px] text-blue-500" />
              </div>

              {/* Video with blob clip */}
              <div
                className="relative z-10 w-[290px] sm:w-[390px] h-[300px] sm:h-[410px] overflow-hidden"
                style={{
                  clipPath: "url(#blobClip)",
                  WebkitClipPath: "url(#blobClip)",
                }}
              >
                <video
                  src="/assets/about-video.mp4"
                  autoPlay
                  muted
                  loop
                  playsInline
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>

          {/* Left Content at bottom - Apply centering styles */}
          <div className="absolute bottom-8 left-0 right-0 bg-white/95 backdrop-blur-sm md:px-8 lg:px-20 py-8 md:py-12 lg:py-16 bg-[url('/grid.png')] bg-repeat">
            <div className="max-w-7xl mx-auto flex flex-col justify-between items-start gap-6 md:gap-8 text-black">
              <div className="flex-1 max-w-md px-0 md:px-4 lg:px-8 xl:px-12">
                <p className="text-xs font-bold text-[#05A0E2] uppercase tracking-wider mb-3">
                  ABOUT US
                </p>
                <h1 className="text-2xl sm:text-3xl font-bold text-black leading-tight mb-4">
                  Your Partner in Platform<br />
                  Engineering & Innovation
                </h1>
                <p className="text-sm text-gray-600 leading-relaxed mb-6">
                  Learn how Prolytech delivers transformative software systems—social, transactional,
                  and intelligent—for high-growth and high-impact businesses.
                </p>
                <button className="inline-block px-6 py-2 text-white font-bold text-xs uppercase tracking-wide rounded-full shadow-lg transition-all duration-300 bg-[#05A0E2] hover:bg-[#0489b8]">
                  LET'S DO IT!
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Apply centering styles */}
        <div className="hidden lg:flex relative z-10 flex-row items-center justify-between h-screen md:px-8 lg:px-20 py-8 md:py-12 lg:py-16 -mt-16">
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center gap-6 md:gap-8 text-black w-full">
            {/* Left Content */}
            <div className="flex-1 max-w-md px-0 md:px-4 lg:px-8 xl:px-12">
              <p className="text-xs font-bold text-[#05A0E2] uppercase tracking-wider mb-3">
                ABOUT US
              </p>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-black leading-tight mb-4">
                Your Partner in Platform<br />
                Engineering & Innovation
              </h1>
              <p className="text-base text-gray-600 leading-relaxed mb-6 max-w-lg">
                Learn how Prolytech delivers transformative software systems—social, transactional,
                and intelligent—for high-growth and high-impact businesses.
              </p>
              <button className="inline-block px-8 py-3 text-white font-bold text-sm uppercase tracking-wide rounded-full shadow-lg transition-all duration-300 bg-[#05A0E2] hover:bg-[#0489b8]">
                LET'S DO IT!
              </button>
            </div>

            {/* Right Side Video - Fixed positioning */}
            <div className="flex-1 flex justify-center items-center relative">
              <div className="relative">
                {/* Background SVG */}
                <div className="absolute z-0 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <AboutBg className="w-[280px] h-[290px] text-blue-500" />
                </div>

                {/* Video with blob clip */}
                <div
                  className="relative z-10 w-[290px] h-[300px] overflow-hidden"
                  style={{
                    clipPath: "url(#blobClip)",
                    WebkitClipPath: "url(#blobClip)",
                  }}
                >
                  <video
                    src="/assets/about-video.mp4"
                    autoPlay
                    muted
                    loop
                    playsInline
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutHero;
